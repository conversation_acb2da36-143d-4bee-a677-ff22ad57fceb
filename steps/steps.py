import json
import time

from behave import given, when, then

from const.vt.vt_data import VTExpectedData
from const.default_data import VTSettingData
from utils.ai_execute_util import AiExecuteUtil
from utils.assert_util import assert_play_start_data, assert_play_end_data
from utils.ck_util import get_ck_data
from utils.data_util import *
from utils.mock_utils import buryingpoint_setmockrule
from loguru import logger


@given('mock规则: {mock_rule}')
def set_mock_rule(context, mock_rule):
    buryingpoint_setmockrule(mock_rule, context.platform)


@given('更新埋点数据: {data}')
def update_extra_data(context, data):
    """
    更新埋点数据, 若已有则覆盖
    :param context:
    :param data: 提供字典类型的字符串, eg: {"quality": 32", "auto_play": 1}
    :return:
    """
    context.vt_expected_data_object.update_vt_expect_data(**eval(data))


@given('打开app')
def start_app(context):
    operator = context.operator
    operator.start_app()


@when('等待{seconds}秒')
def step_impl(context, seconds):
    time.sleep(float(seconds))


@when('观看{seconds}秒')
def step_impl(context, seconds):
    context.played_time = int(seconds)
    time.sleep(float(seconds))


@when('AI执行用例步骤, desc: {desc}')
def ai_execute_case(context, desc):
    logger.warning("AI 暂不可用, 手动执行相关步骤")
    time.sleep(15)
    # ai_executor: AiExecuteUtil = context.ai_executor
    # ai_executor.execute_testcase_by_desc(desc)


@then('播放开始时上报断言')
def step_impl(context):
    platform = context.platform
    # 拿到期望数据
    vt_expected_data_object = context.vt_expected_data_object
    expected_data = vt_expected_data_object.get_vt_expect_data()

    # 获取实际数据并校验
    actual_data = assert_vt_play_start_data(vt_expected_data_object)
    logger.info(f"播放开始时上报断言成功, actual_data: {actual_data}")
    # 从实际值中拿出起始值, 并且直接更新到期望数据中
    vt_expected_data_object.update_expect_play_time_data(
        played_time=actual_data['played_time'],
        total_time=actual_data['total_time'],
        actual_played_time=actual_data['actual_played_time'],
        last_play_progress_time=actual_data['last_play_progress_time'],
        max_play_progress_time=actual_data['max_play_progress_time']
    )


@then('播放结束时上报断言')
def step_impl(context):
    vt_expect_data_object = context.vt_expected_data_object
    # 拿到期望数据
    expected_data = vt_expect_data_object.get_vt_expect_data()
    # 校验数据
    assert_vt_play_end_data(expected_data)


@then('切换ep上报断言')
def step_impl(context):
    vt_expect_data_object = context.vt_expected_data_object
    assert_vt_switch_ep_data(vt_expect_data_object)


def assert_vt_play_start_data(vt_expect_data_object, max_retries=3, retry_delay=5):
    """
    获取并断言埋点数据
    重试机制防止埋点数据上报延迟导致断言失败
    """


    last_exception = None
    last_mismatch = None

    for attempt in range(max_retries + 1):  # +1 因为第一次不算重试
        try:
            logger.info(f"尝试获取埋点数据 (第 {attempt + 1} 次)")


            play_start_actual_data = get_vt_last_actual_data(vt_expect_data_object)
            play_start_expect_data = vt_expect_data_object.get_vt_expect_data()
            result, mis_match = assert_play_start_data(play_start_expect_data, play_start_actual_data)

            if result:
                logger.info(f"埋点数据校验成功 (第 {attempt + 1} 次尝试)")
                # 成功则直接结束, 同时返回实际的数据
                return play_start_actual_data
            else:
                last_mismatch = mis_match
                logger.warning(f"埋点数据校验失败 (第 {attempt + 1} 次尝试), 匹配失败字段: {mis_match}")

                # 如果不是最后一次尝试，则等待后重试
                if attempt < max_retries:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    # 更新查询时间范围，避免获取到相同的旧数据
                    ck_from_time = int(time.time()) - 5 * 60
                else:
                    # 最后一次尝试失败
                    break

        except Exception as e:
            last_exception = e
            logger.error(f"获取埋点数据时发生异常 (第 {attempt + 1} 次尝试): {e}")

            # 如果不是最后一次尝试，则等待后重试
            if attempt < max_retries:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                # 更新查询时间范围
                ck_from_time = int(time.time()) - 5 * 60
            else:
                # 最后一次尝试失败
                break

    # 所有重试都失败了，抛出错误
    if last_exception:
        raise last_exception
    else:
        assert False, f"埋点数据校验失败 (重试 {max_retries} 次后), 最终匹配失败字段: {last_mismatch}"


def assert_vt_play_end_data(vt_expected_data_object, max_retries=5, retry_delay=10):
    """
    获取并断言埋点数据
    重试机制防止埋点数据上报延迟导致断言失败
    需要配合 step: 播放开始上报断言 使用, 因为要依赖那个 step 更新字段
    """

    last_exception = None
    last_mismatch = None

    for attempt in range(max_retries + 1):  # +1 因为第一次不算重试
        try:
            logger.info(f"尝试获取埋点数据 (第 {attempt + 1} 次)")

            play_end_actual_data = get_vt_last_actual_data(vt_expected_data_object)
            play_end_expected_data = vt_expected_data_object.get_vt_expect_data()
            result, mis_match = assert_play_end_data(play_end_expected_data, play_end_actual_data)

            if result:
                logger.info(f"埋点数据校验成功 (第 {attempt + 1} 次尝试)")
                # 成功则直接结束, 同时返回实际的数据
                return play_end_actual_data
            else:
                last_mismatch = mis_match
                logger.warning(f"埋点数据校验失败 (第 {attempt + 1} 次尝试), 匹配失败字段: {mis_match}")

                # 如果不是最后一次尝试，则等待后重试
                if attempt < max_retries:
                    logger.info(f"等待 {retry_delay} 秒后重试...")
                    time.sleep(retry_delay)
                    # 更新查询时间范围，避免获取到相同的旧数据
                    ck_from_time = int(time.time()) - 5 * 60
                else:
                    # 最后一次尝试失败
                    break

        except Exception as e:
            last_exception = e
            logger.error(f"获取埋点数据时发生异常 (第 {attempt + 1} 次尝试): {e}")

            # 如果不是最后一次尝试，则等待后重试
            if attempt < max_retries:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
                # 更新查询时间范围
                ck_from_time = int(time.time()) - 5 * 60
            else:
                # 最后一次尝试失败
                break

    # 所有重试都失败了，抛出错误
    if last_exception:
        raise last_exception
    else:
        assert False, f"埋点数据校验失败 (重试 {max_retries} 次后), 最终匹配失败字段: {last_mismatch}"


def assert_vt_switch_ep_data(vt_expect_data_object, max_retries=3, retry_delay=8):

    last_exception = None
    last_mismatch = None

    for attempt in range(max_retries + 1):  # +1 因为第一次不算重试
        try:
            logger.info(f"尝试获取埋点数据 (第 {attempt + 1} 次)")
            # 使用数据工具获取数据
            actual_data = get_vt_switch_ep_actual_data(vt_expect_data_object)
            expect_data = get_vt_switch_ep_expect_data(vt_expect_data_object)

            # 开始校验数据
            play_end_actual_data = actual_data['play_end_data']
            play_end_expect_data = expect_data['play_end_data']
            result, mis_match = assert_play_end_data(play_end_expect_data, play_end_actual_data)
            if not result:
                logger.warning(f"播放开始埋点数据校验失败 (第 {attempt + 1} 次尝试), 匹配失败字段: {mis_match}")
                last_mismatch = mis_match
                raise AssertionError("播放结束埋点数据校验失败")

            play_start_actual_data = actual_data['play_start_data']
            play_start_expect_data = expect_data['play_start_data']
            result, mis_match = assert_play_start_data(play_start_expect_data, play_start_actual_data)
            if not result:
                logger.warning(f"播放开始埋点数据校验失败 (第 {attempt + 1} 次尝试), 匹配失败字段: {mis_match}")
                last_mismatch = mis_match
                raise AssertionError("播放开始埋点数据校验失败")

            # 检验都通过了, 直接返回
            return
        except Exception as e:
            last_exception = e
            logger.error(f"获取埋点数据时发生异常 (第 {attempt + 1} 次尝试): {e}")

            # 如果不是最后一次尝试，则等待后重试
            if attempt < max_retries:
                logger.info(f"等待 {retry_delay} 秒后重试...")
                time.sleep(retry_delay)
            else:
                # 最后一次尝试失败
                break

    # 所有重试都失败了，抛出错误
    if last_exception:
        raise last_exception
    else:
        assert False, f"埋点数据校验失败 (重试 {max_retries} 次后), 最终匹配失败字段: {last_mismatch}"



if __name__ == '__main__':
    vt_expected_data_object = VTExpectedData("android")

    logger.info(f"初始期望数据:{json.dumps(vt_expected_data_object.get_vt_expect_data())}")

    logger.info("进入视频, 等待 20 s")
    time.sleep(20)

    logger.info("校验播放开始数据")
    # 获取实际数据并校验
    actual_data = assert_vt_play_start_data(vt_expected_data_object)
    logger.info(f"播放开始时上报断言成功, actual_data: {actual_data}")
    # 从实际值中拿出起始值, 并且直接更新到期望数据中
    vt_expected_data_object.update_expect_play_time_data(
        played_time=actual_data['played_time'],
        total_time=actual_data['total_time'],
        actual_played_time=actual_data['actual_played_time'],
        last_play_progress_time=actual_data['last_play_progress_time'],
        max_play_progress_time=actual_data['max_play_progress_time']
    )

    logger.info("退出视频, 等待 20 s")
    time.sleep(20)
    logger.info("校验播放结束数据")
    assert_vt_play_end_data(vt_expected_data_object)


